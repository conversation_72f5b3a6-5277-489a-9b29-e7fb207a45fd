import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navbar = () => {
  const [isMoreDropdownOpen, setIsMoreDropdownOpen] = useState(false);
  const [isLearnDropdownOpen, setIsLearnDropdownOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [scrollDirection, setScrollDirection] = useState('up');
  const [dropdownTimeout, setDropdownTimeout] = useState(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();
  const isHomePage = location.pathname === '/';

  // Handle dropdown closing with delay
  const handleDropdownClose = () => {
    if (dropdownTimeout) {
      clearTimeout(dropdownTimeout);
    }
    const timeout = setTimeout(() => {
      setIsMoreDropdownOpen(false);
      setIsLearnDropdownOpen(false);
    }, 150);
    setDropdownTimeout(timeout);
  };

  const handleDropdownOpen = () => {
    if (dropdownTimeout) {
      clearTimeout(dropdownTimeout);
      setDropdownTimeout(null);
    }
    setIsMoreDropdownOpen(true);
  };

  const handleDropdownStay = () => {
    if (dropdownTimeout) {
      clearTimeout(dropdownTimeout);
      setDropdownTimeout(null);
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Determine scroll direction
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setScrollDirection('down');
        setIsScrolled(true);
      } else if (currentScrollY < lastScrollY) {
        setScrollDirection('up');
        if (currentScrollY <= 50) {
          setIsScrolled(false);
        }
      }

      setLastScrollY(currentScrollY);
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (dropdownTimeout) {
        clearTimeout(dropdownTimeout);
      }
    };
  }, [lastScrollY, dropdownTimeout]);

  return (
    <nav className={`navbar ${isScrolled && scrollDirection === 'down' ? 'navbar-scrolled' : ''}`}>
      <div className="nav-container">
        <Link to="/" className="nav-logo">
          <img src="/BullSeed_LOGO.png" alt="BullSeed" className="nav-logo-image" />
          BullSeed
        </Link>

        {/* Desktop Navigation */}
        <ul className="nav-links desktop-nav">
          <li>
            {isHomePage ? (
              <a href="#features">Features</a>
            ) : (
              <Link to="/#features">Features</Link>
            )}
          </li>
          <li>
            {isHomePage ? (
              <a href="#plans">Plans</a>
            ) : (
              <Link to="/#plans">Plans</Link>
            )}
          </li>
          <li>
            {isHomePage ? (
              <a href="#testimonials">Testimonials</a>
            ) : (
              <Link to="/#testimonials">Testimonials</Link>
            )}
          </li>
          <li
            className="nav-dropdown"
            onMouseEnter={handleDropdownOpen}
            onMouseLeave={handleDropdownClose}
          >
            <a
              href="#"
              className="nav-dropdown-trigger"
              onClick={(e) => {
                e.preventDefault();
                setIsMoreDropdownOpen(!isMoreDropdownOpen);
              }}
            >
              More
              <svg
                width="10"
                height="10"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                className={`nav-dropdown-arrow ${isMoreDropdownOpen ? 'open' : ''}`}
              >
                <polyline points="6,9 12,15 18,9"></polyline>
              </svg>
            </a>
            {isMoreDropdownOpen && (
              <div
                className="nav-dropdown-menu"
                onMouseEnter={handleDropdownStay}
                onMouseLeave={handleDropdownClose}
              >
                <Link to="/about-us">About Us</Link>
                <Link to="/nft-trade">NFT-Trade</Link>
                <Link to="/markets">Markets</Link>
                <div
                  className="nav-dropdown-submenu"
                  onMouseEnter={() => {
                    handleDropdownStay();
                    setIsLearnDropdownOpen(true);
                  }}
                  onMouseLeave={() => setIsLearnDropdownOpen(false)}
                >
                  <a href="#" className="nav-dropdown-submenu-trigger">
                    Learn
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      className="nav-dropdown-arrow-right"
                    >
                      <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                  </a>
                  {isLearnDropdownOpen && (
                    <div className="nav-dropdown-submenu-content">
                      <Link to="/learn/plans">Plans</Link>
                      <Link to="/learn/faq">FAQ</Link>
                    </div>
                  )}
                </div>
                <Link to="/contact">Contact</Link>
              </div>
            )}
          </li>
        </ul>

        <a href="#" className="nav-cta desktop-nav">Start Investing</a>

        {/* Mobile Menu Button */}
        <button
          className="mobile-menu-button"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Toggle mobile menu"
        >
          <span className={`hamburger-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
          <span className={`hamburger-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
          <span className={`hamburger-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
        </button>
      </div>

      {/* Mobile Navigation Menu */}
      <div className={`mobile-nav ${isMobileMenuOpen ? 'open' : ''}`}>
        <div className="mobile-nav-content">
          {/* Close button */}
          <button
            className="mobile-nav-close"
            onClick={() => setIsMobileMenuOpen(false)}
            aria-label="Close mobile menu"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>

          <div className="mobile-nav-links">
            <div className="mobile-nav-section">
              <h3>Navigation</h3>
              {isHomePage ? (
                <a
                  href="#features"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Features
                </a>
              ) : (
                <Link
                  to="/#features"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Features
                </Link>
              )}
              {isHomePage ? (
                <a
                  href="#plans"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Plans
                </a>
              ) : (
                <Link
                  to="/#plans"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Plans
                </Link>
              )}
              {isHomePage ? (
                <a
                  href="#testimonials"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Testimonials
                </a>
              ) : (
                <Link
                  to="/#testimonials"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Testimonials
                </Link>
              )}
            </div>

            <div className="mobile-nav-section">
              <h3>Pages</h3>
              <Link to="/about-us" onClick={() => setIsMobileMenuOpen(false)}>
                About Us
              </Link>
              <Link to="/nft-trade" onClick={() => setIsMobileMenuOpen(false)}>
                NFT Trade
              </Link>
              <Link to="/markets" onClick={() => setIsMobileMenuOpen(false)}>
                Markets
              </Link>
              <Link to="/contact" onClick={() => setIsMobileMenuOpen(false)}>
                Contact
              </Link>
            </div>

            <div className="mobile-nav-section">
              <h3>Learn</h3>
              <Link to="/learn/plans" onClick={() => setIsMobileMenuOpen(false)}>
                Investment Plans
              </Link>
              <Link to="/learn/faq" onClick={() => setIsMobileMenuOpen(false)}>
                FAQ
              </Link>
            </div>
          </div>

          <div className="mobile-nav-cta">
            <a href="#" className="mobile-cta-button">
              Start Investing
            </a>
          </div>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className={`mobile-nav-overlay ${isMobileMenuOpen ? 'open' : ''}`}
          onClick={() => setIsMobileMenuOpen(false)}
        ></div>
      )}
    </nav>
  );
};

export default Navbar;
